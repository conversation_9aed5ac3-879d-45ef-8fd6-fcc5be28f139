<?php
session_start();

// Check if installation is already completed
if (file_exists('install.lock')) {
    die('<div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">
            <h2 style="color: #d9534f;">Installation Already Completed</h2>
            <p>The system has already been installed. If you need to reinstall, please delete the "install.lock" file.</p>
            <a href="login.php" style="background: #5cb85c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Login</a>
         </div>');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'Bijapurpolice';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // Step 1: Test database connection and create database
        try {
            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
            $success = "Database connection successful and database created!";
            $step = 2;
        } catch (PDOException $e) {
            $error = "Database connection failed: " . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Step 2: Import SQL schema - Use simpler approach
        try {
            $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Instead of importing complex SQL, create tables manually
            // This is more reliable than parsing the SQL file

            // Create case_table
            $pdo->exec("CREATE TABLE IF NOT EXISTS `case_table` (
                `case_id` varchar(20) NOT NULL,
                `statement` varchar(200) NOT NULL,
                `caseid` int(11) NOT NULL AUTO_INCREMENT,
                `date_added` datetime DEFAULT NULL,
                `staffid` varchar(30) NOT NULL,
                `case_type` varchar(50) NOT NULL,
                `status` varchar(50) NOT NULL,
                `cid` varchar(20) NOT NULL DEFAULT 'Not Yet',
                `complete_date` date NOT NULL,
                `diary_of_action` varchar(200) NOT NULL,
                PRIMARY KEY (`caseid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // Create complaint table
            $pdo->exec("CREATE TABLE IF NOT EXISTS `complaint` (
                `case_id` varchar(20) NOT NULL,
                `comp_name` varchar(100) NOT NULL,
                `tel` varchar(10) NOT NULL,
                `occupation` varchar(20) NOT NULL,
                `region` varchar(50) NOT NULL,
                `district` varchar(100) NOT NULL,
                `loc` varchar(50) NOT NULL,
                `addrs` varchar(100) NOT NULL,
                `age` int(3) NOT NULL,
                `gender` varchar(6) NOT NULL,
                `date_added` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`case_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // Create crime_type table
            $pdo->exec("CREATE TABLE IF NOT EXISTS `crime_type` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `des` varchar(50) NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // Create investigation table
            $pdo->exec("CREATE TABLE IF NOT EXISTS `investigation` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `case_id` varchar(20) NOT NULL,
                `inv_id` varchar(20) NOT NULL,
                `statement` text NOT NULL,
                `date_added` datetime NOT NULL,
                `status` varchar(50) NOT NULL,
                `officer` varchar(50) NOT NULL,
                `caseid` int(11) NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // Create userlogin table with pre-configured users
            $pdo->exec("CREATE TABLE IF NOT EXISTS `userlogin` (
                `id` int(11) NOT NULL,
                `staffid` varchar(20) NOT NULL,
                `status` varchar(50) NOT NULL,
                `password` varchar(50) NOT NULL,
                `surname` varchar(50) NOT NULL,
                `othernames` varchar(50) NOT NULL,
                PRIMARY KEY (`staffid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // Insert pre-configured users
            $users = [
                ['100', 'Admin', sha1('djain123'), 'Jerry', 'Ofor'],
                ['101', 'Admin', sha1('agarwal123'), 'Harsh', 'Agarwal'],
                ['110', 'CID', sha1('shaikh123'), 'MD Kaif', 'Shaikh'],
                ['113', 'NCO', sha1('ajay123'), 'Henry', 'Okoro'],
                ['114', 'NCO', sha1('usmani123'), 'Aman', 'Usmani'],
                ['115', 'CID', sha1('sukali123'), 'Girish', 'Sukali'],
                ['116', 'CID', sha1('attar123'), 'Abusufiyan', 'Attar'],
                ['117', 'NCO', sha1('bijapur123'), 'Musaveer', 'Bijapur']
            ];

            foreach ($users as $user) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO userlogin (staffid, status, password, surname, othernames) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute($user);
            }

            // Insert some crime types
            $crime_types = ['Murder Case', 'Theft Case', 'Assault Case', 'Defilement', 'Fraud Case', 'Burglary', 'Robbery'];
            foreach ($crime_types as $index => $crime) {
                $stmt = $pdo->prepare("INSERT IGNORE INTO crime_type (id, des) VALUES (?, ?)");
                $stmt->execute([$index + 1, $crime]);
            }

            // Create installation lock file
            file_put_contents('install.lock', date('Y-m-d H:i:s'));

            $success = "Database schema created and users imported successfully! Installation completed.";
            $step = 4; // Skip step 3 since users are already created

        } catch (Exception $e) {
            $error = "Failed to create database schema: " . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Step 3: Create admin user
        $admin_username = trim($_POST['admin_username']);
        $admin_password = trim($_POST['admin_password']);
        $admin_firstname = trim($_POST['admin_firstname']);
        $admin_lastname = trim($_POST['admin_lastname']);
        
        if (empty($admin_username) || empty($admin_password) || empty($admin_firstname) || empty($admin_lastname)) {
            $error = "All fields are required!";
        } elseif (strlen($admin_password) < 6) {
            $error = "Password must be at least 6 characters long!";
        } else {
            try {
                $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Check if admin user already exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM userlogin WHERE staffid = ?");
                $stmt->execute([$admin_username]);
                
                if ($stmt->fetchColumn() > 0) {
                    $error = "Admin username already exists!";
                } else {
                    // Insert admin user
                    $hashed_password = sha1($admin_password);
                    $stmt = $pdo->prepare("INSERT INTO userlogin (staffid, status, password, surname, othernames) VALUES (?, 'Admin', ?, ?, ?)");
                    $stmt->execute([$admin_username, $hashed_password, $admin_lastname, $admin_firstname]);
                    
                    // Create installation lock file
                    file_put_contents('install.lock', date('Y-m-d H:i:s'));
                    
                    $success = "Admin user created successfully! Installation completed.";
                    $step = 4;
                }
            } catch (Exception $e) {
                $error = "Failed to create admin user: " . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRMS Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .install-body {
            padding: 40px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: white;
        }
        .step.active {
            background: #28a745;
        }
        .step.completed {
            background: #17a2b8;
        }
        .step.pending {
            background: #6c757d;
        }
        .btn-install {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s;
        }
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1><i class="fas fa-shield-alt"></i> Crime Record Management System</h1>
            <p>Installation Wizard</p>
        </div>
        
        <div class="install-body">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                <div class="step <?php echo $step >= 4 ? 'completed' : 'pending'; ?>">3</div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <strong>Success:</strong> <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <h3>Step 1: Database Connection</h3>
                <p>This will test the database connection and create the required database.</p>
                <div class="alert alert-info">
                    <strong>Database Configuration:</strong><br>
                    Host: <?php echo $db_host; ?><br>
                    Username: <?php echo $db_user; ?><br>
                    Database: <?php echo $db_name; ?>
                </div>
                <form method="POST">
                    <button type="submit" class="btn btn-install btn-lg w-100">
                        <i class="fas fa-database"></i> Test Connection & Create Database
                    </button>
                </form>

            <?php elseif ($step == 2): ?>
                <h3>Step 2: Import Database Schema</h3>
                <p>This will import all the required tables and data into the database.</p>
                <form method="POST">
                    <input type="hidden" name="import_db" value="1">
                    <button type="submit" class="btn btn-install btn-lg w-100">
                        <i class="fas fa-download"></i> Import Database Schema
                    </button>
                </form>

            <?php elseif ($step == 4): ?>
                <script>
                    // Auto-redirect to login page after 5 seconds
                    window.redirectTimer = setTimeout(function() {
                        window.location.href = 'login.php';
                    }, 5000);
                </script>
                <div class="text-center">
                    <h3 class="text-success">Installation Completed!</h3>
                    <p class="lead">Your Crime Record Management System has been successfully installed.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> You will be automatically redirected to the login page in 5 seconds...
                    </div>

                    <div class="alert alert-success text-start">
                        <h5><i class="fas fa-check-circle"></i> Pre-configured User Accounts:</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead><tr><th>Staff ID</th><th>Name</th><th>Role</th><th>Password</th></tr></thead>
                                <tbody>
                                    <tr><td><strong>100</strong></td><td>Jerry Ofor</td><td><span class="badge bg-danger">Admin</span></td><td><code>djain123</code></td></tr>
                                    <tr><td><strong>101</strong></td><td>Harsh Agarwal</td><td><span class="badge bg-danger">Admin</span></td><td><code>agarwal123</code></td></tr>
                                    <tr><td><strong>110</strong></td><td>MD Kaif Shaikh</td><td><span class="badge bg-warning text-dark">CID</span></td><td><code>shaikh123</code></td></tr>
                                    <tr><td><strong>113</strong></td><td>Henry Okoro</td><td><span class="badge bg-info">NCO</span></td><td><code>ajay123</code></td></tr>
                                    <tr><td><strong>114</strong></td><td>Aman Usmani</td><td><span class="badge bg-info">NCO</span></td><td><code>usmani123</code></td></tr>
                                    <tr><td><strong>115</strong></td><td>Girish Sukali</td><td><span class="badge bg-warning text-dark">CID</span></td><td><code>sukali123</code></td></tr>
                                    <tr><td><strong>116</strong></td><td>Abusufiyan Attar</td><td><span class="badge bg-warning text-dark">CID</span></td><td><code>attar123</code></td></tr>
                                    <tr><td><strong>117</strong></td><td>Musaveer Bijapur</td><td><span class="badge bg-info">NCO</span></td><td><code>bijapur123</code></td></tr>
                                </tbody>
                            </table>
                        </div>
                        <p><strong>Ready to Login:</strong> Use any of these accounts to access the system!</p>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="login.php" class="btn btn-install btn-lg" onclick="clearTimeout(window.redirectTimer);">
                            <i class="fas fa-sign-in-alt"></i> Go to Login Page Now
                        </a>
                        <a href="credentials.php" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> View All Credentials
                        </a>
                    </div>

                    <div class="alert alert-warning mt-4">
                        <strong>Important:</strong> For security reasons, please delete this installation file (install.php) from your server after installation.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
</body>
</html>
