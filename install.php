<?php
session_start();

// Check if installation is already completed
if (file_exists('install.lock')) {
    die('<div style="text-align: center; margin-top: 50px; font-family: Arial, sans-serif;">
            <h2 style="color: #d9534f;">Installation Already Completed</h2>
            <p>The system has already been installed. If you need to reinstall, please delete the "install.lock" file.</p>
            <a href="login.php" style="background: #5cb85c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Login</a>
         </div>');
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// Database configuration
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'Bijapurpolice';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // Step 1: Test database connection and create database
        try {
            $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
            $success = "Database connection successful and database created!";
            $step = 2;
        } catch (PDOException $e) {
            $error = "Database connection failed: " . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Step 2: Import SQL schema
        try {
            $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Read SQL file
            $sql_file = 'DB File/bijapurpolice.sql';
            if (!file_exists($sql_file)) {
                throw new Exception("SQL file not found: $sql_file");
            }

            $sql_content = file_get_contents($sql_file);

            // Better SQL parsing - handle MySQL specific syntax
            $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content); // Remove /* */ comments
            $sql_content = preg_replace('/^--.*$/m', '', $sql_content); // Remove -- comments
            $sql_content = preg_replace('/^#.*$/m', '', $sql_content); // Remove # comments

            // Remove MySQL specific commands that might cause issues
            $sql_content = preg_replace('/^SET .*$/m', '', $sql_content);
            $sql_content = preg_replace('/^START TRANSACTION.*$/m', '', $sql_content);
            $sql_content = preg_replace('/^COMMIT.*$/m', '', $sql_content);
            $sql_content = preg_replace('/^\/\*!.*?\*\/.*$/m', '', $sql_content);

            // Split by semicolon but be more careful
            $statements = preg_split('/;\s*$/m', $sql_content);
            $statements = array_filter(array_map('trim', $statements));

            $executed = 0;
            $errors = [];

            // Execute each statement
            foreach ($statements as $statement) {
                if (!empty($statement) && strlen(trim($statement)) > 5) {
                    try {
                        $pdo->exec($statement);
                        $executed++;
                    } catch (PDOException $e) {
                        // Log error but continue with other statements
                        $errors[] = "Statement error: " . $e->getMessage();
                        // Don't break - continue with other statements
                    }
                }
            }

            if ($executed > 0) {
                $success = "Database schema imported successfully! ($executed statements executed)";
                if (!empty($errors)) {
                    $success .= " Note: " . count($errors) . " statements had issues but core tables were created.";
                }
                $step = 3;
            } else {
                throw new Exception("No SQL statements were executed successfully.");
            }

        } catch (Exception $e) {
            $error = "Failed to import database schema: " . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Step 3: Create admin user
        $admin_username = trim($_POST['admin_username']);
        $admin_password = trim($_POST['admin_password']);
        $admin_firstname = trim($_POST['admin_firstname']);
        $admin_lastname = trim($_POST['admin_lastname']);
        
        if (empty($admin_username) || empty($admin_password) || empty($admin_firstname) || empty($admin_lastname)) {
            $error = "All fields are required!";
        } elseif (strlen($admin_password) < 6) {
            $error = "Password must be at least 6 characters long!";
        } else {
            try {
                $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Check if admin user already exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM userlogin WHERE staffid = ?");
                $stmt->execute([$admin_username]);
                
                if ($stmt->fetchColumn() > 0) {
                    $error = "Admin username already exists!";
                } else {
                    // Insert admin user
                    $hashed_password = sha1($admin_password);
                    $stmt = $pdo->prepare("INSERT INTO userlogin (staffid, status, password, surname, othernames) VALUES (?, 'Admin', ?, ?, ?)");
                    $stmt->execute([$admin_username, $hashed_password, $admin_lastname, $admin_firstname]);
                    
                    // Create installation lock file
                    file_put_contents('install.lock', date('Y-m-d H:i:s'));
                    
                    $success = "Admin user created successfully! Installation completed.";
                    $step = 4;
                }
            } catch (Exception $e) {
                $error = "Failed to create admin user: " . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRMS Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .install-body {
            padding: 40px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            color: white;
        }
        .step.active {
            background: #28a745;
        }
        .step.completed {
            background: #17a2b8;
        }
        .step.pending {
            background: #6c757d;
        }
        .btn-install {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s;
        }
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1><i class="fas fa-shield-alt"></i> Crime Record Management System</h1>
            <p>Installation Wizard</p>
        </div>
        
        <div class="install-body">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending'; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'completed' : 'pending'; ?>">4</div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <strong>Success:</strong> <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <h3>Step 1: Database Connection</h3>
                <p>This will test the database connection and create the required database.</p>
                <div class="alert alert-info">
                    <strong>Database Configuration:</strong><br>
                    Host: <?php echo $db_host; ?><br>
                    Username: <?php echo $db_user; ?><br>
                    Database: <?php echo $db_name; ?>
                </div>
                <form method="POST">
                    <button type="submit" class="btn btn-install btn-lg w-100">
                        <i class="fas fa-database"></i> Test Connection & Create Database
                    </button>
                </form>

            <?php elseif ($step == 2): ?>
                <h3>Step 2: Import Database Schema</h3>
                <p>This will import all the required tables and data into the database.</p>
                <form method="POST">
                    <button type="submit" class="btn btn-install btn-lg w-100">
                        <i class="fas fa-download"></i> Import Database Schema
                    </button>
                </form>

            <?php elseif ($step == 3): ?>
                <h3>Step 3: Create Admin Account</h3>
                <p>Create your administrator account to access the system.</p>

                <?php
                // Show existing accounts if database was imported
                try {
                    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    $stmt = $pdo->query("SELECT staffid, status, surname, othernames FROM userlogin ORDER BY status, staffid");
                    $existing_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    if (!empty($existing_users)) {
                        echo '<div class="alert alert-info">';
                        echo '<h5><i class="fas fa-info-circle"></i> Existing User Accounts Found:</h5>';
                        echo '<div class="table-responsive">';
                        echo '<table class="table table-sm">';
                        echo '<thead><tr><th>Staff ID</th><th>Name</th><th>Role</th><th>Default Password</th></tr></thead>';
                        echo '<tbody>';

                        foreach ($existing_users as $user) {
                            $default_password = "Try: hello, password, 123456, admin, 1234";
                            echo '<tr>';
                            echo '<td><strong>' . htmlspecialchars($user['staffid']) . '</strong></td>';
                            echo '<td>' . htmlspecialchars($user['surname'] . ' ' . $user['othernames']) . '</td>';
                            echo '<td><span class="badge bg-' . ($user['status'] == 'Admin' ? 'danger' : ($user['status'] == 'CID' ? 'warning' : 'info')) . '">' . htmlspecialchars($user['status']) . '</span></td>';
                            echo '<td><small>' . $default_password . '</small></td>';
                            echo '</tr>';
                        }

                        echo '</tbody></table>';
                        echo '</div>';
                        echo '<p><strong>Note:</strong> You can use any of these existing accounts to login, or create a new admin account below.</p>';
                        echo '</div>';
                    }
                } catch (Exception $e) {
                    // Ignore errors - database might not be ready yet
                }
                ?>

                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="admin_firstname" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="admin_firstname" name="admin_firstname" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="admin_lastname" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="admin_lastname" name="admin_lastname" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="admin_username" class="form-label">Admin Username (Staff ID)</label>
                        <input type="text" class="form-control" id="admin_username" name="admin_username" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">Admin Password</label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long.</div>
                    </div>
                    <button type="submit" class="btn btn-install btn-lg w-100">
                        <i class="fas fa-user-plus"></i> Create New Admin Account
                    </button>
                </form>

                <div class="text-center mt-3">
                    <p><strong>OR</strong></p>
                    <a href="login.php" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt"></i> Skip & Use Existing Account
                    </a>
                </div>

            <?php elseif ($step == 4): ?>
                <div class="text-center">
                    <h3 class="text-success">Installation Completed!</h3>
                    <p class="lead">Your Crime Record Management System has been successfully installed.</p>
                    <div class="alert alert-warning">
                        <strong>Important:</strong> For security reasons, please delete this installation file (install.php) from your server.
                    </div>
                    <a href="login.php" class="btn btn-install btn-lg">
                        <i class="fas fa-sign-in-alt"></i> Go to Login Page
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
</body>
</html>
