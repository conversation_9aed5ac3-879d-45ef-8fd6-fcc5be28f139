<!DOCTYPE html>
<!--
Copyright (c) 2003-2021, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>TAB Key-Based Navigation &mdash; CKEditor Sample</title>
	<script src="../../ckeditor.js"></script>
	<link href="sample.css" rel="stylesheet">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
	<style>

		.cke_focused,
		.cke_editable.cke_focused
		{
			outline: 3px dotted blue !important;
			*border: 3px dotted blue !important;	/* For IE7 */
		}

	</style>
	<script>

		CKEDITOR.on( 'instanceReady', function( evt ) {
			var editor = evt.editor;
			editor.setData( 'This editor has it\'s tabIndex set to <strong>' + editor.tabIndex + '</strong>' );

			// Apply focus class name.
			editor.on( 'focus', function() {
				editor.container.addClass( 'cke_focused' );
			});
			editor.on( 'blur', function() {
				editor.container.removeClass( 'cke_focused' );
			});

			// Put startup focus on the first editor in tab order.
			if ( editor.tabIndex == 1 )
				editor.focus();
		});

	</script>
</head>
<body>
	<h1 class="samples">
		<a href="index.html">CKEditor Samples</a> &raquo; TAB Key-Based Navigation
	</h1>
	<div class="warning deprecated">
		This sample is not maintained anymore. Check out its <a href="https://ckeditor.com/docs/ckeditor4/latest/examples/tabindex.html">brand new version in CKEditor Examples</a>.
	</div>
	<div class="description">
		<p>
			This sample shows how tab key navigation among editor instances is
			affected by the <code>tabIndex</code> attribute from
			the original page element. Use TAB key to move between the editors.
		</p>
	</div>
	<p>
		<textarea class="ckeditor" cols="80" id="editor4" rows="10" tabindex="1"></textarea>
	</p>
	<div class="ckeditor" contenteditable="true" id="editor1" tabindex="4"></div>
	<p>
		<textarea class="ckeditor" cols="80" id="editor2" rows="10" tabindex="2"></textarea>
	</p>
	<p>
		<textarea class="ckeditor" cols="80" id="editor3" rows="10" tabindex="3"></textarea>
	</p>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2021, <a class="samples" href="https://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
