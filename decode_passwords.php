<?php
$hashes = array(
    '40bd001563085fc35165329ea1ff5c5ecbdbbeef' => '100 - <PERSON> (Admin)',
    'abab93c9f7ca05823e28d626115b8df017bb6029' => '101 - <PERSON><PERSON><PERSON> (Admin)', 
    '3c2bda6c9791e85f0b5ea4347082ebe68a1e9a04' => '110 - <PERSON> (CID)',
    '2d4f146807cb69cf5ea09357aa9e2c68dc468b96' => '113 - <PERSON> (NCO)',
    '44b6197957aa5465c60e7c7db043e546c341ccdb' => '114 - <PERSON><PERSON> (NCO)',
    'fa765795ac7d2c16547e9df4b233d46803b117bc' => '115 - <PERSON><PERSON><PERSON> (CID)',
    '24aaac9c21ab0a29ed5fae0fcaab75c6a1ec0495' => '116 - <PERSON><PERSON><PERSON><PERSON> (CID)',
    '45d83f57a5fd78f32fbaef4980c03f65b8dd2406' => '117 - Musaveer Bijapur (NCO)'
);

// Extended password list - common passwords used in demo systems
$passwords = array(
    'hello', 'password', '123456', 'admin', '1234', '12345', 'qwerty', 'abc123', 'password123', 'admin123',
    'test', 'user', 'pass', 'login', 'secret', 'demo', 'guest', 'welcome', 'root', 'system',
    'police', 'crms', 'bijapurpolice', 'bijapur', 'staff', 'officer', 'cid', 'nco',
    // Try staff IDs as passwords
    '100', '101', '110', '113', '114', '115', '116', '117',
    // Try names as passwords
    'jerry', 'harsh', 'kaif', 'henry', 'aman', 'girish', 'abusufiyan', 'musaveer',
    'ofor', 'agarwal', 'shaikh', 'okoro', 'usmani', 'sukali', 'attar', 'bijapur'
);

echo "Trying to decode SHA1 hashes...\n\n";

foreach ($hashes as $hash => $user) {
    echo "User: $user\n";
    echo "Hash: $hash\n";
    $found = false;
    
    foreach ($passwords as $pwd) {
        if (sha1($pwd) === $hash) {
            echo "Password: $pwd\n";
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        echo "Password: NOT FOUND (try common passwords manually)\n";
    }
    echo "---\n";
}
?>
