<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRMS - Login Credentials</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .credentials-container {
            max-width: 800px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .credentials-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .credentials-body {
            padding: 40px;
        }
        .role-section {
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
        }
        .role-header {
            padding: 15px 20px;
            font-weight: bold;
            color: white;
        }
        .admin-header { background: #dc3545; }
        .cid-header { background: #ffc107; color: #000; }
        .nco-header { background: #17a2b8; }
        .account-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .account-item:last-child {
            border-bottom: none;
        }
        .account-info {
            flex: 1;
        }
        .account-credentials {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 5px;
            margin-left: 20px;
        }
        .copy-btn {
            margin-left: 10px;
            padding: 4px 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="credentials-container">
        <div class="credentials-header">
            <h1><i class="fas fa-shield-alt"></i> Crime Record Management System</h1>
            <p>Login Credentials Reference</p>
        </div>
        
        <div class="credentials-body">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> System Access Information</h5>
                <p>Below are the pre-configured user accounts for the CRMS system. Use these credentials to login to different user roles.</p>
            </div>

            <!-- Admin Accounts -->
            <div class="role-section">
                <div class="role-header admin-header">
                    <i class="fas fa-user-shield"></i> Administrator Accounts
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Jerry Ofor</strong><br>
                        <small class="text-muted">Full system access, user management</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 100<br>
                        <strong>Password:</strong> djain123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('100', 'djain123')">Copy</button>
                    </div>
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Harsh Agarwal</strong><br>
                        <small class="text-muted">Full system access, user management</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 101<br>
                        <strong>Password:</strong> agarwal123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('101', 'agarwal123')">Copy</button>
                    </div>
                </div>
            </div>

            <!-- CID Accounts -->
            <div class="role-section">
                <div class="role-header cid-header">
                    <i class="fas fa-search"></i> CID Officer Accounts
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>MD Kaif Shaikh</strong><br>
                        <small class="text-muted">Case investigation, report writing</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 110<br>
                        <strong>Password:</strong> shaikh123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('110', 'shaikh123')">Copy</button>
                    </div>
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Girish Sukali</strong><br>
                        <small class="text-muted">Case investigation, report writing</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 115<br>
                        <strong>Password:</strong> sukali123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('115', 'sukali123')">Copy</button>
                    </div>
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Abusufiyan Attar</strong><br>
                        <small class="text-muted">Case investigation, report writing</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 116<br>
                        <strong>Password:</strong> attar123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('116', 'attar123')">Copy</button>
                    </div>
                </div>
            </div>

            <!-- NCO Accounts -->
            <div class="role-section">
                <div class="role-header nco-header">
                    <i class="fas fa-user-tie"></i> NCO (Non-Commissioned Officer) Accounts
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Henry Okoro</strong><br>
                        <small class="text-muted">Complaint registration, case assignment</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 113<br>
                        <strong>Password:</strong> ajay123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('113', 'ajay123')">Copy</button>
                    </div>
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Aman Usmani</strong><br>
                        <small class="text-muted">Complaint registration, case assignment</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 114<br>
                        <strong>Password:</strong> usmani123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('114', 'usmani123')">Copy</button>
                    </div>
                </div>
                <div class="account-item">
                    <div class="account-info">
                        <strong>Musaveer Bijapur</strong><br>
                        <small class="text-muted">Complaint registration, case assignment</small>
                    </div>
                    <div class="account-credentials">
                        <strong>Username:</strong> 117<br>
                        <strong>Password:</strong> bijapur123
                        <button class="btn btn-sm btn-outline-primary copy-btn" onclick="copyCredentials('117', 'bijapur123')">Copy</button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="login.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-sign-in-alt"></i> Go to Login Page
                </a>
                <a href="install.php" class="btn btn-outline-secondary btn-lg ms-2">
                    <i class="fas fa-cog"></i> Installation
                </a>
            </div>

            <div class="alert alert-warning mt-4">
                <h6><i class="fas fa-exclamation-triangle"></i> Security Note:</h6>
                <p class="mb-0">These are default credentials for demonstration purposes. In a production environment, ensure all passwords are changed to secure values.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
    <script>
        function copyCredentials(username, password) {
            const text = `Username: ${username}\nPassword: ${password}`;
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-primary');
                }, 2000);
            });
        }
    </script>
</body>
</html>
