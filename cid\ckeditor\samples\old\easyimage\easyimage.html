<!DOCTYPE html>
<!--
Copyright (c) 2003-2021, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>CKEditor Easy Image Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="../../../samples/css/samples.css">
	<link rel="stylesheet" href="../../../samples/toolbarconfigurator/lib/codemirror/neo.css">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
</head>
<body id="main">

<style>
	.adjoined-bottom:before {
		height: 270px;
	}
</style>

<nav class="navigation-a">
	<div class="grid-container">
		<ul class="navigation-a-left grid-width-70">
			<li><a href="https://ckeditor.com">Project Homepage</a></li>
			<li><a href="https://github.com/ckeditor/ckeditor4/issues">I found a bug</a></li>
			<li><a href="https://github.com/ckeditor/ckeditor4" class="icon-pos-right icon-navigation-a-github">Fork CKEditor on GitHub</a></li>
		</ul>
		<ul class="navigation-a-right grid-width-30">
			<li><a href="https://ckeditor.com/blog/">CKEditor Blog</a></li>
		</ul>
	</div>
</nav>

<header class="header-a">
	<div class="grid-container">
		<h1 class="header-a-logo grid-width-30">
			<img src="../../../samples/img/logo.svg" onerror="this.src='../../../samples/img/logo.png'; this.onerror=null;" alt="CKEditor Sample">
		</h1>
	</div>
</header>

<main>
	<div class="adjoined-top">
		<div class="grid-container">
			<div class="content grid-width-100">
				<h1>Easy Image Demo</h1>
				<p>This sample shows the progress of work on Easy Image. Drop an image file into the editor to see how easily images can be handled.</p>
			</div>
		</div>
	</div>
	<div class="adjoined-bottom">
		<div class="grid-container">
			<div class="grid-width-100">
				<div id="editor">
					<h1>Apollo 11</h1>
					<figure class="image easyimage">
						<img alt="Saturn V carrying Apollo 11" src="../../../samples/img/logo.png">
					</figure>
					<p><strong>Apollo 11</strong> was the spaceflight that landed the first humans, Americans <a href="http://en.wikipedia.org/wiki/Neil_Armstrong">Neil Armstrong</a> and <a href="http://en.wikipedia.org/wiki/Buzz_Aldrin">Buzz Aldrin</a>, on the Moon on July 20, 1969, at 20:18 UTC. Armstrong became the first to step onto the lunar surface 6 hours later on July 21 at 02:56 UTC.</p>
					<figure class="easyimage easyimage-side">
						<img alt="Saturn V carrying Apollo 11" src="../../image2/samples/assets/image1.jpg">
						<figcaption>Saturn V carrying Apollo 11</figcaption>
					</figure>
					<p>Armstrong spent about <s>three and a half</s> two and a half hours outside the spacecraft, Aldrin slightly less; and together they collected 47.5 pounds (21.5&nbsp;kg) of lunar material for return to Earth. A third member of the mission, <a href="http://en.wikipedia.org/wiki/Michael_Collins_(astronaut)">Michael Collins</a>, piloted the <a href="http://en.wikipedia.org/wiki/Apollo_Command/Service_Module">command</a> spacecraft alone in lunar orbit until Armstrong and Aldrin returned to it for the trip back to Earth.</p>
				</div>
			</div>
		</div>
	</div>
</main>

<footer class="footer-a grid-container">
	<div class="grid-container">
		<p class="grid-width-100">
			CKEditor &ndash; The text editor for the Internet &ndash; <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
		</p>
		<p class="grid-width-100" id="copy">
			Copyright &copy; 2003-2021, <a class="samples" href="https://cksource.com/">CKSource</a> &ndash; Frederico Knabben. All rights reserved.
		</p>
	</div>
</footer>
<script>
CKEDITOR.replace( 'editor', {
	plugins: 'easyimage,autogrow,sourcearea,toolbar,undo,wysiwygarea,list,liststyle,format,link,basicstyles,pastefromword',
	toolbar: [
		{ name: 'document', items: [ 'Source', 'Undo', 'Redo' ] },
		{ name: 'basicstyles', items: [ 'Bold', 'Italic', 'Strike', '-', 'RemoveFormat' ] },
		{ name: 'paragraph', items: [ 'NumberedList', 'BulletedList' ] },
		{ name: 'links', items: [ 'Link', 'Unlink', 'Anchor' ] },
		{ name: 'insert', items: [ 'EasyImageUpload' ] },
		{ name: 'styles', items: [ 'Format' ] }
	],
	cloudServices_uploadUrl: 'https://33333.cke-cs.com/easyimage/upload/',
	// Note: this is a token endpoint to be used for CKEditor 4 samples / developer tests only. Images uploaded using the testing token service may be deleted automatically at any moment.
	// To create your own token URL please visit https://ckeditor.com/ckeditor-cloud-services/.
	cloudServices_tokenUrl: 'https://33333.cke-cs.com/token/dev/ijrDsqFix838Gh3wGO3F77FSW94BwcLXprJ4APSp3XQ26xsUHTi0jcb1hoBt',
	height: 500
} );
</script>

</body>
</html>
