/*
++++++++++++++++++++++++++++++++++++++++++++++++++++++
AUTHOR : Vijayan PP
PROJECT : Nim-<PERSON>UTLIT PURPOSE LANDING PAGE TEMPLATE
VERSION : 1.1
++++++++++++++++++++++++++++++++++++++++++++++++++++++
*/



/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	INDEX
++++++++++++++++++++++++++++++++++++++++++++++++++++++
	1> IMPORT FONTS
	2> RESET
	3> GENERAL
        4> TYPOGRAPHY
        5> BUTTON
        6> PRELOADER
	7> NAVIGATION
        8> MAIN HEADER
        9> ABOUT US
       10> OUR WORKS
       11> OUR TEAM
       12> INSPIRATION
       13> TESTIMONIAL
       14> OUR STATS      
       15> SERVICES
       16> CONTACT US
       17> SITE-FOOTER
       18> ANIMATION

       
-------------------------------------------------------*/





/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	IMPORT FONTS
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
@import url('http://fonts.googleapis.com/css?family=Roboto:400,900italic,900,700italic,700,500italic,500,400italic,300italic,300,100italic,100');
@import url('http://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800');


/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	RESET
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/

*{ 
	padding: 0; 
	margin: 0; 
	vertical-align: baseline; 
}

html
{
    -webkit-font-smoothing:antialiased;
}

body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,p,blockquote,hr,th,td {
	margin:0; 
	padding:0;
}

/*++++++++++++++++++++++++++++++++++++++++++++++++++++++
	GENERAL
++++++++++++++++++++++++++++++++++++++++++++++++++++++*/
body {
	color: #fff;
	font: normal 16px "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-weight: 300;
	background-color: #000;
	overflow-x: hidden;
	line-height: 1.5;
        font-family: 'Lato', sans-serif;
	text-rendering: optimizeLegibility !important;
	-webkit-font-smoothing: antialiased !important;
	
}



h1 strong{
	font-weight: 900;
}
h2{
	;
	line-height: 20px;
	margin:  0;
}
h3{
font-size: 1.2em;
font-weight: 900;
}

h5{
	
	font-weight: 700;
	line-height: 20px;
}
p{
font-family: 'Open Sans', sans-serif;
font-size:0.93em;
}


a{ 
  text-decoration: none !important;
  transition:all 0.3s linear;
  -webkit-transition:all 0.3s linear;
  -moz-transition:all 0.3s linear;
  -ms-transition:all 0.3s linear;
   -o-transition:all 0.3s linear;
  
}
ul
{
    list-style: none;
    margin:0;
    padding:0;
}
.text-capitalize
{
    text-transform: uppercase;
}

p.intro{
	font-size:0.9em;
	margin: 14px 0;
	line-height: 24px;
	font-family: 'Open Sans', sans-serif;
}
.relative
{
    position:relative;
}
.wrapper
{
    width:100%;
    height:100%;
    position:relative;
    
}

.rightText
{
text-align:justify;
}
.leftText
{
  text-align:left;
}
.RightText
{
   text-align:right;
}
.top10
{
    margin-top:1.2em;
}
.top20
{
  margin-top:1.3em;
}
.top30
{
  margin-top:2em;
}
.top40
{
    margin-top:2.2em;
}

.margin-top {
	margin-top: 30px;
}
.margin-bottom {
	margin-bottom: 30px;
}

.a-slog {
	font-size:0.93em;
	max-width: 450px;
	margin:0 auto;
        padding-top:10px;
}

.gap
{
    width:100%;
    height:2em;
}
.title {
	font-weight: 700;
	letter-spacing: 0.02em;
	text-transform: uppercase;
	font-family: 'Roboto Slab', serif;
	margin-bottom: 20px;
}
.scroll-block {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
/*	TYPOGRAPHY
------------------------------*/
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6{
	font-family: 'Roboto', sans-serif;
	margin: 0;
}

/*	BUTTON
------------------------------*/
.btn {
    font-size: 14px;
    font-family: 'Source Sans Pro', sans-serif;
    text-transform: uppercase;
    border-radius: 0;
}
.btn:focus,.btn:active {
    outline: none;
    color: #ffffff;
}
.btn-custom{
	border-radius: 3px;
	padding: 0.8em 1.8em;
	color: #fff;
	font-weight: 700;
	-webkit-transition: all .5s ease-out;
	-moz-transition: all .5s ease-out;
	transition: all .5s ease-out;
	text-transform: uppercase;
}
.btn-custom:hover,.btn-custom:focus{
    color: #fff;
}
.btn-custom-outline {
	border-radius: 3px;
	padding: 0.8em 1.8em;
	color: #fff;
	font-weight: 700;
	-webkit-transition: all .5s ease-out;
	transition: all .5s ease-out;
	text-transform: uppercase;
	background-color: transparent;
	border-color: #ffffff;
}
.btn-custom-outline:hover,.btn-custom-outline:focus{
	color: #ffffff;
	background-color: rgba(255,255,255,.5);
}
.btn-bar a{
	margin-right: 10px;
}

/*	PRELOADER
------------------------------*/

div.preloader {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    overflow: visible;
    background-color: #f7f7f7;
}

@-webkit-keyframes loader {
    0%, 10%, 100% {
        width: 80px;
        height: 80px;
    }
    65% {
        width: 150px;
        height: 150px;
    }
}

@keyframes loader {
    0%, 10%, 100% {
        width: 80px;
        height: 80px;
    }
    65% {
        width: 150px;
        height: 150px;
    }
}

.loader {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    -webkit-transform: translate(-50%, -50%) rotate(45deg) translate3d(0, 0, 0);
    transform: translate(-50%, -50%) rotate(45deg) translate3d(0, 0, 0);
    -webkit-animation: loader 1.2s infinite ease-in-out;
    animation: loader 1.2s infinite ease-in-out;
    border-radius:50%;
    -webkit-border-radius:50%;
    -moz-border-radius:50%;
}

.loader span {
    position: absolute;
    display: block;
    width: 40px;
    height: 40px;
    left:50%;
    margin-left:-20px;
    top:50%;
    margin-top:20px;
    border-radius:50%;
    -webkit-animation: loaderBlock 1.2s infinite ease-in-out both;
    animation: loaderBlock 1.2s infinite ease-in-out both;
    background:#000;
}

.loader span:nth-child(1) {
    top: 0;
    left: 0;
}

.loader span:nth-child(2) {
    top: 0;
    right: 0;
    -webkit-animation: loaderBlockInverse 1.2s infinite ease-in-out both;
    animation: loaderBlockInverse 1.2s infinite ease-in-out both;
}

.loader span:nth-child(3) {
    bottom: 0;
    left: 0;
    -webkit-animation: loaderBlockInverse 1.2s infinite ease-in-out both;
    animation: loaderBlockInverse 1.2s infinite ease-in-out both;
}

.loader span:nth-child(4) {
    bottom: 0;
    right: 0;
}


/*	RAMSH-STYLE STARTS HERE
------------------------------*/
 
/* Navigation */
.nim-menu{
	padding: 10px;
	transition: all 0.8s;
        background:#000;
        border-bottom:none;
        transition:all 0.4s ease-in;
        -webkit-transition:all 0.4s ease-in;
        -moz-transition:all 0.4s ease-in;
        -ms-transition:all 0.4s ease-in;
         
}

.nim-menu a.navbar-brand {
text-transform: uppercase;
font-size: 1.5em;
font-weight: 900;
color:#fff;
}



.nim-menu.navbar-default .navbar-nav > li > a {
	
	color: #fafafa;
	letter-spacing: 1px;
        font-size:.73em;
       //text-shadow:1px 0px rgba(0,0,0,0.5);
}

.navbar-default .navbar-nav > .active > a, 
.navbar-default .navbar-nav > .active > a:hover, 
.navbar-default .navbar-nav > .active > a:focus {
	
	background-color: transparent;
	font-weight: 700;
        
}

.navbar-toggle {
	border-radius:20%;
        transition:all 1s;
        
	}
.navbar-default .navbar-toggle:hover, 
.navbar-default .navbar-toggle:focus {
	box-shadow:2px 2px 3px 3px rgba(0,0,0,0.4);
        -webkit-box-shadow:2px 2px 3px 3px rgba(0,0,0,0.4);
        -moz-box-shadow:2px 2px 3px 3px rgba(0,0,0,0.4);
        -ms-box-shadow:2px 2px 3px 3px rgba(0,0,0,0.4);
	}
.navbar-default .navbar-toggle:hover>.icon-bar {
	background-color: #FFF;
	}
        
        
       
/*	MAIN HEADING
------------------------------*/



.main-heading
{
   background: url(../images/main.jpg) center center no-repeat;
	background-size:cover;
	background-attachment: fixed;
	color: #cfcfcf;
        margin-top:80px;
}

.overlay{
	background: -moz-linear-gradient(top,  rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.73) 17%, rgba(0,0,0,0.66) 35%, rgba(0,0,0,0.55) 62%, rgba(0,0,0,0.4) 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0.8)), color-stop(17%,rgba(0,0,0,0.73)), color-stop(35%,rgba(0,0,0,0.66)), color-stop(62%,rgba(0,0,0,0.55)), color-stop(100%,rgba(0,0,0,0.4))); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  rgba(0,0,0,0.8) 0%,rgba(0,0,0,0.73) 17%,rgba(0,0,0,0.66) 35%,rgba(0,0,0,0.55) 62%,rgba(0,0,0,0.4) 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  rgba(0,0,0,0.8) 0%,rgba(0,0,0,0.73) 17%,rgba(0,0,0,0.66) 35%,rgba(0,0,0,0.55) 62%,rgba(0,0,0,0.4) 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  rgba(0,0,0,0.8) 0%,rgba(0,0,0,0.73) 17%,rgba(0,0,0,0.66) 35%,rgba(0,0,0,0.55) 62%,rgba(0,0,0,0.4) 100%); /* IE10+ */
	background: linear-gradient(to bottom,  rgba(0,0,0,0.8) 0%,rgba(0,0,0,0.73) 17%,rgba(0,0,0,0.66) 35%,rgba(0,0,0,0.55) 62%,rgba(0,0,0,0.4) 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cc000000', endColorstr='#66000000',GradientType=0 ); /* IE6-9 */
	//background-attachment: fixed;
        height:auto;
        height:100%;
        position:relative;
}

.overlay > .container
{
    padding:170px 0px;    
}

.main-heading >.overlay
{
    height: 750px;
}
p.lead
{
   position:relative;
   top:5px;
   color:#e3e3e3;
   font-size:0.93em;
   
}

.main-heading-content {
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	position: absolute;
	top: 30%;
	left: 0;
	right: 0;
}

.main-heading-title {
	font-weight: 900;
	color: #ffffff;
	text-transform: uppercase;
	font-size:3.5em;
	text-shadow: rgba(0,0,0,0.498039) 2px 2px 2px;
	letter-spacing: 0.02em;
	
}
.main-heading-title span {
	margin-left: 6px;
}
.main-heading-text {
	font-size: 16px;
	color: #ffffff;
	margin-bottom: 20px;
}
.main-cursor {
	opacity: 1;
	-webkit-animation: blink .6s infinite;
	-moz-animation: blink .6s infinite;
	-ms-animation: blink .6s infinite;
	-o-animation: blink .6s infinite;
	animation: blink .6s infinite;
}

/*----------------ABOUT US

-------------------------------------------------------*/
.aboutus>.container
{
    padding:60px 0px;
  
}



 .about-box {
	overflow: hidden;
	clear: both;
	margin-bottom: 30px;
}
.about-box i {
	font-size: 36px;
}
.about-box .about-line {
	border-left: 1px solid #eee;
	border-right: 1px solid #eee;
}
.about-box h4
{
    padding-top:10px;
}
.about-box p{
	font-size:0.93em;
        padding-top:10px;
}

/*----------------OUR WORKS

-------------------------------------------------------*/

.recent-works>.container
{
    padding:70px 0px;
}

.item-img-wrap{
    position: relative;
    text-align: center;
    overflow: hidden;
}
.item-img-wrap img{
    -webkit-transition:all 200ms linear; 
    -moz-transition:all 200ms linear; 
    -o-transition:all 200ms linear; 
    -ms-transition:all 200ms linear; 
    transition:all 200ms linear; 
   
}
.item-img-overlay{
    position: absolute;
    width:100%;
    height: 100%;
    left: 0;
    top: 0;
    
}
.item-img-overlay span{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background:#222 url("../images/plus.png") center center no-repeat;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    opacity: 0;
    z-index:99;
    -webkit-transition: opacity 250ms linear;
    transition: opacity 250ms linear;
}
.item-img-wrap:hover .item-img-overlay span{
    opacity: 0.6;
   
}
.item-img-wrap:hover img{
    -moz-transform: scale(1.1); 
    -webkit-transform: scale(1.1); 
    -o-transform: scale(1.1); 
    transform: scale(1.1); 
}

.work-desc{
    width: 100%;
    padding: 10px 20px;
    border-top: none;
    -webkit-border-bottom-right-radius: 5px;
	-webkit-border-bottom-left-radius: 5px;
	-moz-border-radius-bottomright: 5px;
	-moz-border-radius-bottomleft: 5px;
	border-bottom-right-radius: 5px;
	border-bottom-left-radius: 5px;
    position: relative;
}
.work-desc h3{
    margin: 0;
    padding: 0;
    font-size: 0.93em;
    font-weight: 700;
    text-transform: capitalize;
    font-family: 'Roboto Slab', serif;
    margin-bottom: 5px;
    
}

.work-desc h3 a
{
    color:#fff;
}
.work-desc span {
	margin-top: 10px;
        font-size:0.82em;
}



/*----------------OUR TEAM

-------------------------------------------------------*/
.our-team>.container
{
    padding:70px 0px;
}

.our-team h4  {
    font-family: 'Roboto Slab', serif;
   }

.our-team h4,.our-team h5
{
    padding-top:15px;
}
.our-team p{
	font-size:0.83em;
        padding-top:15px;
}
.list-inline a
{
    color:#000;
}

/*----------------INSPIRATION

-------------------------------------------------------*/
.inspiration
{
 background:url("../images/img-1.jpg");
 background-position: center center;
 background-attachment: fixed;
 background-size:cover;
}
.inspiration >.overlay.container
{
    padding:80px 0px;
}

.subheading h4 {
	color: #ffffff;
	font-style: italic;
	font-family: 'Roboto Slab', serif;
}
.intermediate-container .heading h2 {
	font-size: 40px;
	line-height: 47px;
	text-transform: uppercase;
	margin-top: 30px;
	color: #ffffff;
	margin-bottom: 20px;
	font-weight: 700;
}

/*	TESTIMONIAL
------------------------------*/
.client-testimonial
{
 background:url("../images/img-2.jpg");
 background-position: center center;
 background-attachment: fixed;
 background-size:cover;
}

.client-testimonial .container
{
 padding:70px 0px;
}


#testimonial {
	padding: 30px 0;
}
#testimonial .item{
    display: block;
    width: 100%;
    height: auto;
}

#testimonial .item p {
	font-weight: 400;
	margin: 30px 0;
	color: #d1d1d1;
}

/*	STATS
------------------------------*/
.our-stats {
    background: #35434f;
}

.our-stats>.container
{
    padding:60px 0px;
}

.our-stat-icon .fa {
    font-size: 60px;
    color: #fff;
    line-height: 90px;
}

.our-stat-info span {
    color: #fff;
    font-size: 32px;
    margin: 1em 0;
    line-height: 60px;
}

.our-stat-info h5 {
    color: #fff;
    line-height: 3em;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0 0;
}

/*  Services
----------------------------------------------------------------------------- */
.services >.container
{
    padding:60px;
}

.nim-service i.fa {
	float: left;
	font-size:1.6em;
	width: 70px;
        height:70px;
        background:#000;
        border-radius:50%;
        color:#fff; 
        text-align: center;
        padding-top:25px;
        transition:all 200ms ease-in;
        -webkit-transition:all 200ms ease-in;
        -moz-transition:all 200ms ease-in;
        -ms-transition:all 200ms ease-in;
}
.nim-service .nim-service-detail {
	margin-left:90px;
}
.nim-service .nim-service-detail h4 {
font-family: 'Roboto Slab', serif;
}
.nim-service .nim-service-detail p {
	color: #9aa7af;
        margin-top:10px;
        font-size:0.83em;
        color:#202020;
}

.nim-service:hover>i.fa
{
    background:#222 url("../images/5.jpg") center center no-repeat;
    color:#000;
    box-shadow:2px 3px 5px 8px rgba(0,0,0,0.3);
    -webkit-box-shadow:2px 3px 5px 2px rgba(0,0,0,0.3);
    -moz-box-shadow:2px 3px 5px 2px rgba(0,0,0,0.3);
    -ms-box-shadow:2px 3px 5px 2px rgba(0,0,0,0.3);
}

/*----------------CONTACTUS

-------------------------------------------------------*/
.sub-form
{
    background:#222;
}
.sub-form>.container
{
    padding:80px 0px;
}

.form-control::-webkit-input-placeholder {
 color: #8A8A8A;
}
 .form-control:-moz-placeholder {
 color: #8A8A8A;
}
 .form-control::-moz-placeholder {
 color: #8A8A8A;
}
 .form-control:-ms-input-placeholder {
 color: #8A8A8A;
}

.form-control {
  background-color: #222;
  color: #4c4c4c;
  
  box-shadow: none;
  height: 60px;
  font-weight: 400;
  font-size: 16px;
  padding: 0 1.5em;
}
#mc-form
{
    margin-top:50px;
    margin-left:auto;
    margin-right:auto;
    
}

.form-control:focus {
  border-color: #fa5454;
  outline: 0;
  box-shadow: none;
}

.center-block
{
    margin-left:auto;
    margin-right:auto;
}

.sub-form .btn
{ 
    padding: 1em; 
}

#mc-form .btn-default
{
    height: 60px;
}

#mc-notification {
  margin: 0.75em 0 0;
  font-weight: 400;
  color: #8A8A8A;
  font-size: 17px;
}

.error { color: #FF4C4C !important; }

.valid { color: #4CAF50 !important; }

.error i,
.valid i { padding-right: .5em; }

/*----------------SITE FOOTER

-------------------------------------------------------*/
.site-footer
{
    background:#fafafa;
    font-size:0.93em;
}
.site-footer >.container
{
    padding:30px 0px;
}

.site-footer small
{ 
    display: inline-block;
}

.footer-links,.site-footer small
{ 
    margin: 0 0 .75em;
}

.site-footer small,.footer-links a
{
  color: #8A8A8A;
  font-weight: 300;
  font-size:0.93em;
}

.footer-links a:hover
{ 
    color: #fa5454;
}

.footer-links a:first-child 
{
    padding-right: .4em;
}

/*----------------SOCIAL

-------------------------------------------------------*/

.social li {
  display: inline-block;
  padding-right: .5em;
  text-align: center;
}

.social li:last-child { padding-right: 0; }

.social li a {
  display: block;
  width: 45px;
  height: 45px;
  line-height: 45px;
  color: #fff;
  border-radius: 50%;
  position: relative;
  transition: all .8s ease;
  font-size: 1.3em;
  background: #D6D6D6;
}

.social li a:hover {

  background:#000;
}


/*----------------ANIMATIONS

-------------------------------------------------------*/
@-webkit-keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 1; }
    50.01% { opacity: 0; }
    100% { opacity: 0; }
}
@-moz-keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 1; }
    50.01% { opacity: 0; }
    100% { opacity: 0; }
}
@-ms-keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 1; }
    50.01% { opacity: 0; }
    100% { opacity: 0; }
}
@-o-keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 1; }
    50.01% { opacity: 0; }
    100% { opacity: 0; }
}
@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 1; }
    50.01% { opacity: 0; }
    100% { opacity: 0; }
}

